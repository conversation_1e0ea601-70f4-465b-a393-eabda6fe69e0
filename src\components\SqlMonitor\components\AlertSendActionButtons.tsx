import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

import { tableStyles } from '../styles';

interface AlertSendActionButtonsProps {
  selectedCount: number;
  onAddAlertSend: () => void;
  onBatchDelete: () => void;
  onClearSelection: () => void;
}

/**
 * 告警发送操作按钮组件
 * 包含新增、批量删除等操作按钮
 */
export const AlertSendActionButtons: React.FC<AlertSendActionButtonsProps> = ({
  selectedCount,
  onAddAlertSend,
  onBatchDelete,
  onClearSelection,
}) => {
  return (
    <div className="flex-shrink-0 h-12 flex items-center justify-between px-4 bg-white border-t border-gray-200">
      {/* 左侧：新增告警发送按钮 */}
      <div className="flex items-center gap-2">
        <Button
          type="primary"
          size="middle"
          icon={<PlusOutlined />}
          onClick={onAddAlertSend}
          className={`${tableStyles.addTaskBtn} w-40 h-8 flex items-center justify-center`}
        >
          新增告警发送
        </Button>
      </div>

      {/* 右侧：批量操作区域 - 当有选中项时显示 */}
      {selectedCount > 0 && (
        <div className={tableStyles.batchOperations}>
          <div className="flex items-center gap-4 h-full">
            <div className="flex items-center gap-2">
              <div className={tableStyles.pulseDot}></div>
              <span className="text-blue-700 font-medium">
                已选择 {selectedCount} 项（跨页面选择）
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                type="text"
                danger
                onClick={onBatchDelete}
                className={tableStyles.batchDeleteBtn}
                icon={<DeleteOutlined />}
              >
                批量删除
              </Button>
              <Button type="text" onClick={onClearSelection} className={tableStyles.batchCancelBtn}>
                取消全选
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
