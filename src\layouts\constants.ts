import React from 'react';
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  TeamOutlined,
  ProfileOutlined,
  LogoutOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  SendOutlined,
  InfoCircleOutlined,
  BellOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import type { RouteMapping } from './types';

/**
 * 路由配置映射 - 统一管理路由、标签页和路径的映射关系
 */
export const ROUTE_CONFIG = {
  dashboard: { path: '/dashboard', label: '仪表板' },
  users: { path: '/users', label: '用户管理' },
  tasks: { path: '/tasks', label: '任务管理' },
  alerts: { path: '/alerts', label: '告警管理' },
  'db-connections': { path: '/db-connections', label: '数据库连接' },
  'alert-sends': { path: '/alert-sends', label: '告警发送' },
  'other-infos': { path: '/other-infos', label: '其他信息' },
  teams: { path: '/teams', label: '团队协作' },
  'refactored-test': { path: '/refactored-test', label: '重构测试' },
} as const;

/**
 * 路由与标签页的映射关系 - 从 ROUTE_CONFIG 自动生成
 */
export const ROUTE_TAB_MAPPING: RouteMapping = {
  '/': { key: 'dashboard', label: ROUTE_CONFIG.dashboard.label },
  ...Object.entries(ROUTE_CONFIG).reduce((acc, [key, config]) => {
    acc[config.path] = { key, label: config.label };
    return acc;
  }, {} as RouteMapping),
};

/**
 * 标签页标题映射 - 从 ROUTE_CONFIG 自动生成
 */
export const TAB_LABELS: Record<string, string> = Object.entries(ROUTE_CONFIG).reduce(
  (acc, [key, config]) => {
    acc[key] = config.label;
    return acc;
  },
  {} as Record<string, string>
);

/**
 * 根据 key 获取对应的路径
 */
export const getPathByKey = (key: string): string => {
  if (key === 'dashboard') return '/dashboard';
  return ROUTE_CONFIG[key as keyof typeof ROUTE_CONFIG]?.path || '/dashboard';
};

/**
 * 根据路径获取对应的 key
 */
export const getKeyByPath = (path: string): string => {
  if (path === '/' || path === '/dashboard') return 'dashboard';

  const entry = Object.entries(ROUTE_CONFIG).find(([, config]) => config.path === path);
  return entry ? entry[0] : 'dashboard';
};

/**
 * 侧边栏菜单配置
 */
export const getSidebarMenuItems = (): MenuProps['items'] => [
  {
    key: 'dashboard',
    icon: React.createElement(DashboardOutlined),
    label: '仪表板',
  },
  {
    key: 'tasks',
    icon: React.createElement(FileTextOutlined),
    label: '任务管理',
  },
  {
    key: 'alerts',
    icon: React.createElement(BellOutlined),
    label: '告警管理',
  },
  {
    key: 'db-connections',
    icon: React.createElement(DatabaseOutlined),
    label: '数据库连接',
  },
  {
    key: 'alert-sends',
    icon: React.createElement(SendOutlined),
    label: '告警发送',
  },
  {
    key: 'other-infos',
    icon: React.createElement(InfoCircleOutlined),
    label: '其他信息',
  },
  {
    key: 'users',
    icon: React.createElement(UserOutlined),
    label: '用户管理',
  },
  {
    key: 'teams',
    icon: React.createElement(TeamOutlined),
    label: '团队协作',
  },
  {
    key: 'sub1',
    label: '系统设置',
    icon: React.createElement(SettingOutlined),
    children: [
      {
        key: 'settings-basic',
        label: '基础设置',
      },
      {
        key: 'settings-permission',
        label: '权限管理',
      },
      {
        key: 'settings-logs',
        label: '系统日志',
      },
    ],
  },
];

/**
 * 用户下拉菜单配置
 */
export const getUserMenuItems = (): MenuProps['items'] => [
  {
    key: 'profile',
    icon: React.createElement(ProfileOutlined),
    label: '个人资料',
  },
  {
    key: 'settings',
    icon: React.createElement(SettingOutlined),
    label: '账户设置',
  },
  {
    type: 'divider',
  },
  {
    key: 'logout',
    icon: React.createElement(LogoutOutlined),
    label: '退出登录',
    danger: true,
  },
];

/**
 * 默认用户信息
 */
export const DEFAULT_USER = {
  name: '管理员',
  avatar: undefined,
  role: 'admin',
};

/**
 * 布局常量
 */
export const LAYOUT_CONSTANTS = {
  SIDER_WIDTH: 240,
  SIDER_COLLAPSED_WIDTH: 48,
  HEADER_HEIGHT: 48,
  TABS_HEIGHT: 30,
  CONTENT_MARGIN: 12,
  LOGO_TEXT: 'DB SQL MONITOR',
  LOGO_TEXT_COLLAPSED: 'S',
} as const;
