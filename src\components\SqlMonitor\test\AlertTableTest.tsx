/**
 * AlertTable 组件测试文件
 * 用于验证组件的基本功能和导入是否正确
 */

import React from 'react';

// 测试所有相关的导入
import AlertTable from '../components/AlertTable';
import { AlertActionButtons } from '../components/AlertActionButtons';
import { createAlertTableColumns } from '../components/AlertTableColumns';
import { AlertTableComponent } from '../components/AlertTableComponent';
import { AlertQuickSearchForm, AlertAdvancedSearchForm } from '../components/forms/AlertSearchForm';
import AlertBasicForm from '../components/forms/AlertBasicForm';
import { AlertModalManager } from '../components/modals/AlertModalManager';

// 测试 hooks 导入
import { useAlertData } from '../hooks/useAlertData';
import { useAlertTable } from '../hooks/useAlertTable';
import { useSelection } from '../hooks/useSelection';

// 测试类型导入
import type { TaskAlert, TaskAlertSearchParams } from '../types';

// 测试服务导入
import { TaskService } from '../services';

/**
 * 简单的组件测试
 */
const AlertTableTest: React.FC = () => {
  console.log('AlertTable 组件导入测试成功');
  console.log('所有相关组件、hooks、类型和服务都已正确导入');

  // 测试类型定义
  const testAlert: TaskAlert = {
    id: 1,
    name: '测试告警',
    severity: 'high',
    sql: 'SELECT COUNT(*) FROM test_table',
    alert_type: 'isValue',
    values: ['=0'],
    create_time: '2024-01-01 00:00:00',
    update_time: '2024-01-01 00:00:00',
  };

  const testSearchParams: TaskAlertSearchParams = {
    current: 1,
    page_size: 10,
    name: '测试',
    severity: 'high',
    type: 'isValue',
  };

  console.log('类型测试:', { testAlert, testSearchParams });

  return (
    <div style={{ padding: '20px' }}>
      <h2>AlertTable 组件测试</h2>
      <p>✅ 所有组件导入成功</p>
      <p>✅ 所有 hooks 导入成功</p>
      <p>✅ 所有类型定义正确</p>
      <p>✅ 服务导入成功</p>

      <div style={{ marginTop: '20px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
        <AlertTable contentHeight={600} />
      </div>
    </div>
  );
};

export default AlertTableTest;
