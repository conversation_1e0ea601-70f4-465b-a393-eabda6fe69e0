/**
 * 任务管理服务
 * 提供所有任务相关的API调用
 */

import type {
  TaskBasic,
  TaskBasicFormData,
  TaskBasicFormDataAdd,
  TaskBasicFormDataUpdateOrDelete,
  TaskBasicGroupFormData,
  TaskBasicSearchParams,
  TaskGroupSearchParams,
  TaskAlert,
  DBConnection,
  AlertSend,
  OtherInfo,
  TaskApiResponse,
  TaskDeleteResponse,
  TaskAlertSearchParams,
  ApiResponse,
} from '../types';

import { httpClient } from './httpClient';
import {
  mockAlertData,
  mockDbConnectionData,
  mockAlertSendData,
  mockOtherInfoData,
} from './mockData';

// 导入频率转换工具
import { parseFrequencyFromString } from '../../../utils/frequencyConverter';

/**
 * 任务管理API服务类
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param params 搜索参数
   * @returns 任务列表和总数
   */
  static async getTasks(params: TaskBasicSearchParams): Promise<TaskApiResponse<TaskBasic>> {
    console.log('获取任务列表参数:', params);

    // 模拟API调用延迟
    await httpClient.delay(300);

    try {
      const response = await httpClient.post<TaskApiResponse<TaskBasicFormData>>(
        '/api/v1/task/exec/select',
        params
      );

      console.log('获取任务列表响应:', response);

      if (!response || !response.data || response.data == null) {
        console.warn('获取任务数据失败：返回数据为空');
        return {
          data: [],
          total: 0,
          success: true,
        };
      }

      // 将 TaskBasicFormData 转换为 TaskBasic
      const tableData: TaskBasic[] = response.data.map((item: TaskBasicFormData) => {
        // 解析执行频率字符串为对象格式
        const parsedFrequency = parseFrequencyFromString(item.frequency, false);
        const parsedRetryFrequency = parseFrequencyFromString(item.retry_frequency, true);

        return {
          id: item.id,
          name: item.name,
          group_id: item.group_id,
          group_name: item.group_name,
          status: item.status as 'enabled' | 'disabled',
          start_time: item.start_time,
          end_time: item.end_time,
          weekday: item.weekday ? item.weekday.split(',') : [],
          frequency: parsedFrequency || { value: 0, unit: '分' }, // 使用默认值
          retry_num: item.retry_num,
          retry_frequency: parsedRetryFrequency || { value: 0, unit: '分钟' }, // 使用默认值
          alert_task_id: item.alert_task_id ? item.alert_task_id.split(',') : [],
          alert_send_id: item.alert_send_id ? item.alert_send_id.split(',') : [],
          db_connection_id: item.db_connection_id,
          other_info_id: item.other_info_id,
          create_time: item.create_time,
          update_time: item.update_time,
        };
      });

      return {
        data: tableData,
        total: response.total,
        success: true,
      };
    } catch (error) {
      console.error('获取任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务分组列表
   * @param params 搜索参数
   * @returns 任务分组列表和总数
   */
  static async getTaskGroups(
    params: TaskGroupSearchParams
  ): Promise<TaskApiResponse<TaskBasicGroupFormData>> {
    console.log('获取任务分组参数:', params);

    // 模拟API调用延迟
    await httpClient.delay(300);

    try {
      const response = await httpClient.post<TaskApiResponse<TaskBasicGroupFormData>>(
        '/api/v1/task/group/select',
        params
      );

      console.log('获取任务分组响应:', response);

      return {
        data: response.data,
        total: response.total,
        success: true,
      };
    } catch (error) {
      console.error('获取任务分组失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有任务分组
   * @returns 所有任务分组列表
   */
  static async getTaskGroupAll(): Promise<TaskApiResponse<TaskBasicGroupFormData>> {
    // 模拟API调用延迟
    await httpClient.delay(300);

    try {
      const response = await httpClient.post<TaskApiResponse<TaskBasicGroupFormData>>(
        '/api/v1/task/group/select/all',
        {}
      );

      console.log('获取所有任务分组响应:', response);

      return {
        data: response.data,
        total: response.total,
        success: true,
      };
    } catch (error) {
      console.error('获取所有任务分组失败:', error);
      throw error;
    }
  }

  /**
   * 删除单个任务
   * @param id 任务ID
   * @returns 删除结果
   */
  static async deleteTask(id: number): Promise<TaskApiResponse<TaskDeleteResponse>> {
    console.log('删除任务:', id);

    // 模拟API调用延迟
    await httpClient.delay(500);

    try {
      // 请求后端参数包装成map
      const params = {
        ids: [id],
      };

      const response = await httpClient.post<TaskApiResponse<TaskDeleteResponse>>(
        '/api/v1/task/exec/delete/id',
        params
      );

      console.log('删除任务响应:', response);
      return response;
    } catch (error) {
      console.error('删除任务失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   * @returns 删除结果
   */
  static async batchDeleteTasks(ids: number[]): Promise<TaskApiResponse<TaskDeleteResponse>> {
    console.log('批量删除任务:', ids);

    // 模拟API调用延迟
    await httpClient.delay(800);

    try {
      // 请求后端参数包装成map
      const params = {
        ids: ids,
      };

      const response = await httpClient.post<TaskApiResponse<TaskDeleteResponse>>(
        '/api/v1/task/exec/delete/id',
        params
      );

      console.log('批量删除任务响应:', response);
      return response;
    } catch (error) {
      console.error('批量删除任务失败:', error);
      throw error;
    }
  }

  /**
   * 添加任务
   * @param taskData 任务数据
   * @returns 新创建的任务信息
   */
  static async addTask(taskData: TaskBasicFormDataAdd): Promise<TaskBasicFormDataAdd> {
    console.log('添加任务:', taskData);

    // 模拟API调用延迟
    await httpClient.delay(600);

    try {
      const response = await httpClient.post<TaskBasicFormDataAdd>('/api/v1/task/exec', taskData);

      console.log('添加任务响应:', response);
      return response;
    } catch (error) {
      console.error('添加任务失败:', error);
      throw error;
    }
  }

  /**
   * 更新任务
   * @param id 任务ID
   * @param taskData 任务数据
   * @returns 更新后的任务信息
   */
  static async updateTask(
    id: number,
    taskData: TaskBasicFormDataUpdateOrDelete
  ): Promise<TaskBasicFormDataUpdateOrDelete> {
    console.log('更新任务:', id, taskData);

    // 模拟API调用延迟
    await httpClient.delay(800);

    try {
      const response = await httpClient.post<TaskBasicFormDataUpdateOrDelete>(
        '/api/v1/task/exec/update',
        taskData
      );

      console.log('更新任务响应:', response);
      return response;
    } catch (error) {
      console.error('更新任务失败:', error);
      throw error;
    }
  }

  /**
   * 保存复合表单数据
   * @param data 表单数据
   * @returns 保存结果
   */
  static async saveComplexForm(data: TaskBasicFormDataAdd): Promise<TaskBasicFormDataAdd> {
    console.log('保存复合表单数据:', data);

    await httpClient.delay(800);

    try {
      const response = await httpClient.post<TaskBasicFormDataAdd>('/api/v1/task/exec', data);

      console.log('保存复合表单响应:', response);
      return response;
    } catch (error) {
      console.error('保存复合表单失败:', error);
      throw error;
    }
  }

  /**
   * 更新复合表单数据
   * @param id 任务ID
   * @param data 表单数据
   * @returns 更新结果
   */
  static async updateComplexForm(
    id: number,
    data: TaskBasicFormDataUpdateOrDelete
  ): Promise<TaskBasicFormDataUpdateOrDelete> {
    console.log('更新复合表单数据:', id, data);

    await httpClient.delay(800);

    try {
      const response = await httpClient.post<TaskBasicFormDataUpdateOrDelete>(
        '/api/v1/task/exec/update',
        data
      );

      console.log('更新复合表单响应:', response);
      return response;
    } catch (error) {
      console.error('更新复合表单失败:', error);
      throw error;
    }
  }

  // ==================== 告警相关方法 ====================

  /**
   * 获取告警列表
   * @returns 告警列表
   */
  static async getAlerts(
    params: Partial<TaskAlertSearchParams>
  ): Promise<TaskApiResponse<TaskAlert>> {
    await httpClient.delay(200);

    console.log(params);
    const res = await httpClient.post<TaskApiResponse<TaskAlert>>(
      '/api/v1/task/alert/select',
      params
    );

    console.log(res, 99);

    return res;
  }

  /**
   * 根据ID数组获取告警列表
   * @param ids 告警ID数组
   * @returns 告警列表
   */
  static async getAlertsByIds(ids: string[]): Promise<TaskAlert[]> {
    await httpClient.delay(200);
    // 模拟根据ID查询
    return mockAlertData.filter(alert => ids.includes(`alert_${alert.id}`));
  }

  /**
   * 删除单个告警
   * @param id 告警ID
   * @returns 删除结果
   */
  static async deleteAlert(id: number): Promise<ApiResponse<TaskDeleteResponse>> {
    console.log('删除告警:', id);
    await httpClient.delay(500);

    const response = await httpClient.post<TaskDeleteResponse>('/api/v1/task/alert/delete/id', {
      ids: [id],
    });
    console.log('删除告警响应:', response);

    return response;
  }

  /**
   * 批量删除告警
   * @param ids 告警ID数组
   * @returns 删除结果
   */
  static async batchDeleteAlerts(ids: number[]): Promise<ApiResponse<TaskDeleteResponse>> {
    console.log('批量删除告警:', ids);
    await httpClient.delay(800);

    const response = await httpClient.post<TaskDeleteResponse>('/api/v1/task/alert/delete/id', {
      ids: ids,
    });
    console.log('批量删除告警响应:', response);
    return response;
  }

  // ==================== 数据库连接相关方法 ====================

  /**
   * 获取所有数据库连接列表
   * @returns 数据库连接列表
   */
  static async getDbConnections(): Promise<DBConnection[]> {
    await httpClient.delay(200);
    return mockDbConnectionData;
  }

  /**
   * 根据数字ID获取单个数据库连接
   * @param id 数据库连接ID
   * @returns 数据库连接信息
   */
  static async getDbConnectionByIdNumber(id: number): Promise<DBConnection | null> {
    await httpClient.delay(200);
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => conn.id === id);
    return connection || null;
  }

  /**
   * 根据字符串ID获取数据库连接
   * @param id 数据库连接ID
   * @returns 数据库连接信息
   */
  static async getDbConnectionById(id: string): Promise<DBConnection | null> {
    await httpClient.delay(200);
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => `db_${conn.id}` === id);
    return connection || null;
  }

  // ==================== 告警发送相关方法 ====================

  /**
   * 获取告警发送列表
   * @returns 告警发送列表
   */
  static async getAlertSends(): Promise<AlertSend[]> {
    await httpClient.delay(200);
    return mockAlertSendData;
  }

  /**
   * 根据ID数组获取告警发送列表
   * @param ids 告警发送ID数组
   * @returns 告警发送列表
   */
  static async getAlertSendsByIds(ids: string[]): Promise<AlertSend[]> {
    await httpClient.delay(200);
    // 模拟根据ID查询
    return mockAlertSendData.filter(send => ids.includes(`send_${send.id}`));
  }

  // ==================== 其他信息相关方法 ====================

  /**
   * 获取其他信息列表
   * @returns 其他信息列表
   */
  static async getOtherInfos(): Promise<OtherInfo[]> {
    await httpClient.delay(200);
    return mockOtherInfoData;
  }

  /**
   * 根据ID获取其他信息
   * @param id 其他信息ID
   * @returns 其他信息
   */
  static async getOtherInfoById(id: string): Promise<OtherInfo | null> {
    await httpClient.delay(200);
    // 模拟根据ID查询
    const info = mockOtherInfoData.find(info => `info_${info.id}` === id);
    return info || null;
  }

  // ==================== 删除方法 ====================

  /**
   * 删除单个数据库连接
   * @param id 数据库连接ID
   * @returns 删除结果
   */
  static async deleteDbConnection(id: number): Promise<TaskDeleteResponse> {
    console.log('删除数据库连接:', id);
    await httpClient.delay(500);

    try {
      const response = await httpClient.delete<TaskDeleteResponse>(`/api/v1/db-connection/${id}`);
      console.log('删除数据库连接响应:', response);
      return response;
    } catch (error) {
      console.error('删除数据库连接失败:', error);
      // 模拟成功响应
      return { total: 1, success: true, message: '删除成功' };
    }
  }

  /**
   * 批量删除数据库连接
   * @param ids 数据库连接ID数组
   * @returns 删除结果
   */
  static async batchDeleteDbConnections(ids: number[]): Promise<TaskDeleteResponse> {
    console.log('批量删除数据库连接:', ids);
    await httpClient.delay(800);

    try {
      const response = await httpClient.post<TaskDeleteResponse>(
        '/api/v1/db-connection/batch-delete',
        {
          ids,
        }
      );
      console.log('批量删除数据库连接响应:', response);
      return response;
    } catch (error) {
      console.error('批量删除数据库连接失败:', error);
      // 模拟成功响应
      return { total: ids.length, success: true, message: '批量删除成功' };
    }
  }

  /**
   * 删除单个告警发送配置
   * @param id 告警发送配置ID
   * @returns 删除结果
   */
  static async deleteAlertSend(id: number): Promise<TaskDeleteResponse> {
    console.log('删除告警发送配置:', id);
    await httpClient.delay(500);

    try {
      const response = await httpClient.delete<TaskDeleteResponse>(`/api/v1/alert-send/${id}`);
      console.log('删除告警发送配置响应:', response);
      return response;
    } catch (error) {
      console.error('删除告警发送配置失败:', error);
      // 模拟成功响应
      return { total: 1, success: true, message: '删除成功' };
    }
  }

  /**
   * 批量删除告警发送配置
   * @param ids 告警发送配置ID数组
   * @returns 删除结果
   */
  static async batchDeleteAlertSends(ids: number[]): Promise<TaskDeleteResponse> {
    console.log('批量删除告警发送配置:', ids);
    await httpClient.delay(800);

    try {
      const response = await httpClient.post<TaskDeleteResponse>(
        '/api/v1/alert-send/batch-delete',
        {
          ids,
        }
      );
      console.log('批量删除告警发送配置响应:', response);
      return response;
    } catch (error) {
      console.error('批量删除告警发送配置失败:', error);
      // 模拟成功响应
      return { total: ids.length, success: true, message: '批量删除成功' };
    }
  }

  /**
   * 删除单个其他信息配置
   * @param id 其他信息配置ID
   * @returns 删除结果
   */
  static async deleteOtherInfo(id: number): Promise<TaskDeleteResponse> {
    console.log('删除其他信息配置:', id);
    await httpClient.delay(500);

    try {
      const response = await httpClient.delete<TaskDeleteResponse>(`/api/v1/other-info/${id}`);
      console.log('删除其他信息配置响应:', response);
      return response;
    } catch (error) {
      console.error('删除其他信息配置失败:', error);
      // 模拟成功响应
      return { total: 1, success: true, message: '删除成功' };
    }
  }

  /**
   * 批量删除其他信息配置
   * @param ids 其他信息配置ID数组
   * @returns 删除结果
   */
  static async batchDeleteOtherInfos(ids: number[]): Promise<TaskDeleteResponse> {
    console.log('批量删除其他信息配置:', ids);
    await httpClient.delay(800);

    try {
      const response = await httpClient.post<TaskDeleteResponse>(
        '/api/v1/other-info/batch-delete',
        {
          ids,
        }
      );
      console.log('批量删除其他信息配置响应:', response);
      return response;
    } catch (error) {
      console.error('批量删除其他信息配置失败:', error);
      // 模拟成功响应
      return { total: ids.length, success: true, message: '批量删除成功' };
    }
  }

  // ==================== 数据库连接 CRUD 方法 ====================

  /**
   * 添加数据库连接
   * @param data 数据库连接数据
   * @returns 新创建的数据库连接信息
   */
  static async addDbConnection(data: any): Promise<DBConnection> {
    console.log('添加数据库连接:', data);
    await httpClient.delay(600);

    try {
      const response = await httpClient.post<DBConnection>('/api/v1/db-connection', data);
      console.log('添加数据库连接响应:', response);
      return response;
    } catch (error) {
      console.error('添加数据库连接失败:', error);
      // 模拟成功响应
      return {
        id: Date.now(),
        ...data,
        create_time: new Date().toISOString(),
        update_time: new Date().toISOString(),
      };
    }
  }

  /**
   * 更新数据库连接
   * @param id 数据库连接ID
   * @param data 更新数据
   * @returns 更新后的数据库连接信息
   */
  static async updateDbConnection(id: number, data: any): Promise<DBConnection> {
    console.log('更新数据库连接:', id, data);
    await httpClient.delay(600);

    try {
      const response = await httpClient.put<DBConnection>(`/api/v1/db-connection/${id}`, data);
      console.log('更新数据库连接响应:', response);
      return response;
    } catch (error) {
      console.error('更新数据库连接失败:', error);
      // 模拟成功响应
      return {
        id,
        ...data,
        update_time: new Date().toISOString(),
      };
    }
  }

  // ==================== 告警发送 CRUD 方法 ====================

  /**
   * 添加告警发送配置
   * @param data 告警发送配置数据
   * @returns 新创建的告警发送配置信息
   */
  static async addAlertSend(data: any): Promise<AlertSend> {
    console.log('添加告警发送配置:', data);
    await httpClient.delay(600);

    try {
      const response = await httpClient.post<AlertSend>('/api/v1/alert-send', data);
      console.log('添加告警发送配置响应:', response);
      return response;
    } catch (error) {
      console.error('添加告警发送配置失败:', error);
      // 模拟成功响应
      return {
        id: Date.now(),
        ...data,
        create_time: new Date().toISOString(),
        update_time: new Date().toISOString(),
      };
    }
  }

  /**
   * 更新告警发送配置
   * @param id 告警发送配置ID
   * @param data 更新数据
   * @returns 更新后的告警发送配置信息
   */
  static async updateAlertSend(id: number, data: any): Promise<AlertSend> {
    console.log('更新告警发送配置:', id, data);
    await httpClient.delay(600);

    try {
      const response = await httpClient.put<AlertSend>(`/api/v1/alert-send/${id}`, data);
      console.log('更新告警发送配置响应:', response);
      return response;
    } catch (error) {
      console.error('更新告警发送配置失败:', error);
      // 模拟成功响应
      return {
        id,
        ...data,
        update_time: new Date().toISOString(),
      };
    }
  }

  // ==================== 其他信息 CRUD 方法 ====================

  /**
   * 添加其他信息配置
   * @param data 其他信息配置数据
   * @returns 新创建的其他信息配置信息
   */
  static async addOtherInfo(data: any): Promise<OtherInfo> {
    console.log('添加其他信息配置:', data);
    await httpClient.delay(600);

    try {
      const response = await httpClient.post<OtherInfo>('/api/v1/other-info', data);
      console.log('添加其他信息配置响应:', response);
      return response;
    } catch (error) {
      console.error('添加其他信息配置失败:', error);
      // 模拟成功响应
      return {
        id: Date.now(),
        ...data,
        create_time: new Date().toISOString(),
        update_time: new Date().toISOString(),
      };
    }
  }

  /**
   * 更新其他信息配置
   * @param id 其他信息配置ID
   * @param data 更新数据
   * @returns 更新后的其他信息配置信息
   */
  static async updateOtherInfo(id: number, data: any): Promise<OtherInfo> {
    console.log('更新其他信息配置:', id, data);
    await httpClient.delay(600);

    try {
      const response = await httpClient.put<OtherInfo>(`/api/v1/other-info/${id}`, data);
      console.log('更新其他信息配置响应:', response);
      return response;
    } catch (error) {
      console.error('更新其他信息配置失败:', error);
      // 模拟成功响应
      return {
        id,
        ...data,
        update_time: new Date().toISOString(),
      };
    }
  }
}
